'use server';

import { dbConnect } from "@/shared/lib/database";
import { VillagePeopleSchema } from "@/entity/village-people/schema";

export type CreateVillagePeopleParams = {
    name: string;
    phone: string;
    mahalla: string;
    street: string;
};

export type CreateVillagePeopleResult = {
    success: boolean;
    data?: {
        _id: string;
        index: string;
        name: string;
        phone: string;
        mahalla: string;
        street: string;
        createdAt: string;
        updatedAt: string;
    };
    error?: string;
};

/**
 * Создает новый VillagePeople в базе данных
 */
export async function createVillagePeople(params: CreateVillagePeopleParams): Promise<CreateVillagePeopleResult> {
    try {
        await dbConnect();

        const { index, name, phone, mahalla, street } = params;

        // Проверяем, не существует ли уже VillagePeople с таким индексом
        const existingVillagePeople = await VillagePeopleSchema.findOne({ index });
        if (existingVillagePeople) {
            return {
                success: false,
                error: "Bu indeks bilan xo'jalik allaqachon mavjud"
            };
        }

        // Проверяем, не существует ли уже VillagePeople с таким телефоном
        const existingPhone = await VillagePeopleSchema.findOne({ phone });
        if (existingPhone) {
            return {
                success: false,
                error: "Bu telefon raqami bilan xo'jalik allaqachon mavjud"
            };
        }

        // Создаем новый VillagePeople
        const villagePeople = await VillagePeopleSchema.create({
            index,
            name,
            phone,
            mahalla,
            street,
        });

        return {
            success: true,
            data: {
                _id: villagePeople._id.toString(),
                index: villagePeople.index,
                name: villagePeople.name,
                phone: villagePeople.phone,
                mahalla: villagePeople.mahalla,
                street: villagePeople.street,
                createdAt: villagePeople.createdAt.toISOString(),
                updatedAt: villagePeople.updatedAt.toISOString(),
            }
        };
    } catch (error) {
        console.error('Error creating village people:', error);
        return {
            success: false,
            error: (error as Error).message || "Xo'jalik yaratishda xatolik yuz berdi"
        };
    }
}
