'use server';

import {dbConnect} from "@/shared/lib/database";
import {IVillagePeople, VillagePeopleSchema} from "@/entity/village-people/schema";
import {PaginationList} from "@/shared/api/external";

export type TVillagePeopleListParams = {
    page?: number;
    size?: number;
    search?: string;
}

export type IVillagePeopleListModel = {
    _id: string;
    index: string;
    name: string;
    phone: string;
    mahalla: string;
    street: string;
}

export async function getVillagePeopleListPaginated (params: TVillagePeopleListParams) {
    const { page = 1, size = 20, search = '' } = params;

    const searchRegex = new RegExp(search, 'i');
    const filter = search
        ? {
            $or: [
                { name: searchRegex },
                { phone: searchRegex },
                { mahalla: searchRegex },
                { street: searchRegex },
            ],
        }
        : {};

    await dbConnect()

    const skip = (page - 1) * size;

    const [total, dataRaw] = await Promise.all([
        VillagePeopleSchema.countDocuments(filter),
        VillagePeopleSchema.find(filter)
            .skip(skip)
            .limit(size)
            .sort({ name: 1 })
            .lean()
    ])

    const data = (dataRaw as IVillagePeople[]).map((doc) => ({
        _id: doc._id.toString() as string,
        index: doc.index,
        name: doc.name,
        phone: doc.phone,
        mahalla: doc.mahalla,
        street: doc.street,
    })) as IVillagePeopleListModel[]

    return {
        data,
        total: total as number,
        totalPages: Math.ceil(total / size),
        currentPage: page as number
    } as PaginationList<IVillagePeopleListModel>
}

export async function getVillagePeopleList (searchParams: Promise<TVillagePeopleListParams>) {
    const params = await searchParams
    return getVillagePeopleListPaginated(params)
}