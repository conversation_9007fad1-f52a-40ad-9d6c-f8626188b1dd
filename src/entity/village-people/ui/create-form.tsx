"use client"

import React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/shared/ui/button"
import { Input } from "@/shared/ui/input"
import { Label } from "@/shared/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card"
import { Loader2 } from "lucide-react"
import { createVillagePeopleSchema, CreateVillagePeopleFormValues } from "../model/create-schema"
import { useCreateVillagePeople } from "../model/use-create-village-people"
import { IMaskInput } from "react-imask"

type CreateVillagePeopleFormProps = {
    onSuccess?: () => void
    onCancel?: () => void
}

export function CreateVillagePeopleForm({ onSuccess, onCancel }: CreateVillagePeopleFormProps) {
    const createMutation = useCreateVillagePeople()

    // Используем react-hook-form с zod для валидации
    const {
        register,
        handleSubmit,
        setValue,
        watch,
        reset,
        formState: { errors, isSubmitting }
    } = useForm<CreateVillagePeopleFormValues>({
        resolver: zodResolver(createVillagePeopleSchema),
        defaultValues: {
            index: "",
            name: "",
            phone: "",
            mahalla: "",
            street: "",
        }
    })

    // Обработчик отправки формы
    const onSubmit = async (data: CreateVillagePeopleFormValues) => {
        const result = await createMutation.mutateAsync({
            index: data.index,
            name: data.name,
            phone: data.phone.startsWith('+') ? data.phone : `+${data.phone}`,
            mahalla: data.mahalla,
            street: data.street,
        })

        if (result.success) {
            reset()
            onSuccess?.()
        }
    }

    return (
        <Card className="w-full max-w-2xl mx-auto">
            <CardHeader>
                <CardTitle>Yangi xo'jalik qo'shish</CardTitle>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    {/* Индекс */}
                    <div className="space-y-2">
                        <Label htmlFor="index">Indeks *</Label>
                        <Input
                            id="index"
                            {...register("index")}
                            placeholder="Masalan: 001"
                            disabled={isSubmitting}
                        />
                        {errors.index && (
                            <p className="text-sm text-red-500">{errors.index.message}</p>
                        )}
                    </div>

                    {/* Имя */}
                    <div className="space-y-2">
                        <Label htmlFor="name">Ism *</Label>
                        <Input
                            id="name"
                            {...register("name")}
                            placeholder="To'liq ism va familiya"
                            disabled={isSubmitting}
                        />
                        {errors.name && (
                            <p className="text-sm text-red-500">{errors.name.message}</p>
                        )}
                    </div>

                    {/* Телефон */}
                    <div className="space-y-2">
                        <Label htmlFor="phone">Telefon raqami *</Label>
                        <IMaskInput
                            mask="+{998}000000000"
                            placeholder="+998901234567"
                            disabled={isSubmitting}
                            onAccept={(value) => setValue("phone", value)}
                            render={(ref, props) => (
                                <Input
                                    {...props}
                                    ref={ref}
                                    id="phone"
                                />
                            )}
                        />
                        {errors.phone && (
                            <p className="text-sm text-red-500">{errors.phone.message}</p>
                        )}
                    </div>

                    {/* Махалля */}
                    <div className="space-y-2">
                        <Label htmlFor="mahalla">Mahalla *</Label>
                        <Input
                            id="mahalla"
                            {...register("mahalla")}
                            placeholder="Mahalla nomi"
                            disabled={isSubmitting}
                        />
                        {errors.mahalla && (
                            <p className="text-sm text-red-500">{errors.mahalla.message}</p>
                        )}
                    </div>

                    {/* Улица */}
                    <div className="space-y-2">
                        <Label htmlFor="street">Ko'cha *</Label>
                        <Input
                            id="street"
                            {...register("street")}
                            placeholder="Ko'cha nomi"
                            disabled={isSubmitting}
                        />
                        {errors.street && (
                            <p className="text-sm text-red-500">{errors.street.message}</p>
                        )}
                    </div>

                    {/* Кнопки */}
                    <div className="flex gap-2 pt-4">
                        <Button
                            type="submit"
                            disabled={isSubmitting}
                            className="flex-1"
                        >
                            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                            {isSubmitting ? "Saqlanmoqda..." : "Saqlash"}
                        </Button>
                        {onCancel && (
                            <Button
                                type="button"
                                variant="outline"
                                onClick={onCancel}
                                disabled={isSubmitting}
                                className="flex-1"
                            >
                                Bekor qilish
                            </Button>
                        )}
                    </div>
                </form>
            </CardContent>
        </Card>
    )
}
