"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/ui/table"
import { SimplePagination } from "@/shared/ui/pagination"
import { Card, CardContent } from "@/shared/ui/card"
import { PaginationList } from "@/shared/api/external";
import { useQueryParams } from "@/shared/lib/use-query-params";
import { IVillagePeopleListModel, TVillagePeopleListParams } from "@/entity/village-people/api/list";
import Link from "next/link";
import { Button } from "@/shared/ui/button";
import { Eye } from "lucide-react";


type Props = {
    villagePeople: PaginationList<IVillagePeopleListModel>
}

export const VillagePeopleList = ({villagePeople}: Props) => {
    const {queryParams, setQueryParams} = useQueryParams<TVillagePeopleListParams>()
    const currentPage = Number(queryParams.page || '1')
    const rowsPerPage = Number(queryParams.size || '20')

    const currentItems = villagePeople.data

    // Handle page change
    const handlePageChange = (page: number) => {
        setQueryParams({
            page: page.toString()
        })
    }

    const handleRowsPerPageChange = (value: string) => {
        setQueryParams({
            page: '1',
            size: value
        })
    }

    return (
        <>
            <h1 className="text-3xl font-bold mb-8">{"Xo'jaliklar: "} {villagePeople.total}</h1>

            {/* Desktop Table View */}
            <div className="hidden md:block">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[80px]">T/R</TableHead>
                            <TableHead>F.I.SH</TableHead>
                            <TableHead>Telefon raqami</TableHead>
                            <TableHead>Mahalla</TableHead>
                            <TableHead className="w-[100px] text-right">Amallar</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <>
                            {currentItems.map((person, index) => (
                                <TableRow key={person._id}>
                                    <TableCell>{(currentPage - 1) * rowsPerPage + index + 1}</TableCell>
                                    <TableCell>{person.name}</TableCell>
                                    <TableCell>{person.phone}</TableCell>
                                    <TableCell>{person.mahalla}</TableCell>
                                    <TableCell className="text-right">
                                        <Link href={`/dashboard/village-people/${person._id}`}>
                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 cursor-pointer">
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                        </Link>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </>
                    </TableBody>
                </Table>
            </div>

            {/* Mobile Card View */}
            <div className="md:hidden space-y-4">
                {currentItems.map((person) => (
                    <Card key={person._id} className="overflow-hidden py-1">
                        <CardContent className="p-4">
                            <div className="flex justify-between items-start">
                                <div>
                                    <h3 className="font-medium">{person.name}</h3>
                                    <div className="text-sm text-muted-foreground">
                                        <p>Telefon raqami: {person.phone}</p>
                                        <p>{person.mahalla}</p>
                                    </div>
                                </div>
                                <Link href={`/dashboard/village-people/${person._id}`}>
                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                        <Eye className="h-4 w-4" />
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>
            <SimplePagination
                currentPage={currentPage}
                rowsPerPage={rowsPerPage}
                totalPages={villagePeople.totalPages}
                handlePageChange={handlePageChange}
                handleRowsPerPageChange={handleRowsPerPageChange}
                contentLength={villagePeople.data.length}
                total={villagePeople.total}
            />
        </>
    )
}

