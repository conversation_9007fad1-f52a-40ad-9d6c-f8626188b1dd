import {Schema, Document, model, models, Model} from 'mongoose';

export type IVillagePeople = Document & {
    _id: string;
    index: string;
    name: string;
    phone: string;
    mahalla: string;
    street: string;
    updatedAt: Date;
    createdAt: Date;
}

const VillagePeople: Schema = new Schema({
    index: { type: String, required: true },
    name: { type: String, required: true },
    phone: { type: String, required: true },
    mahalla: { type: String, required: true },
    street: { type: String, required: true },
}, { collection: 'villagePeople' });

export const VillagePeopleSchema = (models.villagePeople || model('villagePeople', VillagePeople)) as Model<IVillagePeople>
