"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { CreateVillagePeopleForm } from "@/entity/village-people/ui"
import { But<PERSON> } from "@/shared/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function CreateVillagePeoplePage() {
    const router = useRouter()

    const handleSuccess = () => {
        router.push("/dashboard/village-people")
    }

    const handleCancel = () => {
        router.back()
    }

    return (
        <div className="container mx-auto py-3 px-2 sm:px-4">
            <div className="flex justify-between items-center mb-6">
                <Link href="/dashboard/village-people">
                    <Button variant="outline" size="sm" className="flex items-center gap-1 cursor-pointer h-8">
                        <ArrowLeft className="h-3 w-3" />
                        {"Ro'yxatga qaytish"}
                    </Button>
                </Link>
            </div>

            <CreateVillagePeopleForm
                onSuccess={handleSuccess}
                onCancel={handleCancel}
            />
        </div>
    )
}
