import {ReactNode} from "react";
import {Sidebar, SidebarContent, SidebarProvider, SidebarTrigger} from "@/shared/ui/sidebar";
import {ThemeToggle} from "@/shared/ui/theme-toggle";

type LayoutProps = {
    children: ReactNode
    content?: ReactNode
}

export function Layout({ children, content }: LayoutProps) {
    return (
        <SidebarProvider>
            <Sidebar>
                <SidebarContent>
                    {content}
                </SidebarContent>
            </Sidebar>
            <main className='w-full'>
                <SidebarTrigger className='fixed' />
                <div className='container mx-auto py-8 px-4'>
                    <ThemeToggle />
                    {children}
                </div>
            </main>
        </SidebarProvider>
    )
}