'use server';

import qs from 'query-string'
import {
    ExternalInitiativeTokenModel,
    ExternalInitiativeTokenParams,
    ExternalVoteCaptcha,
    ExternalVoteParams,
    ExternalVotesModel
} from "@/shared/api/external/types";

export async function getCaptcha (): Promise<ExternalVoteCaptcha> {
    const res = await fetch("https://openbudget.uz/api/v2/vote/captcha-2", {
        "headers": {
            "accept": "application/json, text/plain, */*",
            "accept-language": "ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7",
            "access-captcha": "czBlODRrODRyODRlMTExdA==",
            "authorization": "",
            "cache-control": "no-cache",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "sec-ch-ua": "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Linux\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "cookie": "route=13218e5c614a22ea47d848931320747f",
            "Referer": "https://openbudget.uz/boards/initiatives/initiative/50/192db02b-019f-4645-8490-69f6e72902ad",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        },
        "method": "GET"
    });

    if(!res.ok){
        throw new Error(`Captchani olishda xatolik! Keyinroq urinib ko'ring`)
    }

    return res.json()

    // {
    //     "image": "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",
    //     "captchaKey": "67d598b3e35e7976f7200662"
    // }
}

export async function getInitiativeToken (body: ExternalInitiativeTokenParams): Promise<ExternalInitiativeTokenModel> {
    const res = await fetch("https://openbudget.uz/api/v2/info/get-initiative-token", {
        "headers": {
            "accept": "application/json, text/plain, */*",
            "accept-language": "ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7",
            "authorization": "",
            "cache-control": "no-cache",
            "content-type": "application/json",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "sec-ch-ua": "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Linux\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "cookie": "route=c5ff4ce17d26f2b0e652903b4d260673",
            "Referer": "https://openbudget.uz/boards/initiatives/initiative/50/192db02b-019f-4645-8490-69f6e72902ad",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        },
        body: JSON.stringify(body),
        // "body": "{\"initiativeId\":\"192db02b-019f-4645-8490-69f6e72902ad\",\"captchaKey\":\"67d598b3e35e7976f7200662\",\"captchaResult\":\"5\"}",
        "method": "POST"
    });

    if(!res.ok){
        throw new Error(`Captcha xato yechilgan! Qaytadan urinib ko'ring`)
    }

    return res.json()

    // {
    //     "token": "67d598def7e8d3111a8db269",
    //     "date": "2025-03-15T21:12:30.152+05:00"
    // }
}

export async function getVotes ({token, ...query}: ExternalVoteParams): Promise<{content: ExternalVotesModel[]}> {
    const res = await fetch(`https://openbudget.uz/api/v2/info/votes-2/${token}?${qs.stringify(query)}`, {
        "headers": {
            "accept": "application/json, text/plain, */*",
            "accept-language": "ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7",
            "authorization": `Bearer ${token}`,
            "cache-control": "no-cache",
            "content-length": "0",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "sec-ch-ua": "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Linux\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "cookie": "route=13218e5c614a22ea47d848931320747f",
            "Referer": "https://openbudget.uz/boards/initiatives/initiative/50/192db02b-019f-4645-8490-69f6e72902ad",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        },
        "method": "GET"
    });

    if(!res.ok){
        throw new Error(`Ovozlarni olishda xatolik! Keyinroq urinib ko'ring`)
    }

    return res.json()
}