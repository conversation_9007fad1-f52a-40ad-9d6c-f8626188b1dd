import { Vote, Users } from "lucide-react"
import Link from "next/link";

import {
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from "@/shared/ui/sidebar"

// Menu items.
const items = [
    {
        title: "Ovozlar",
        url: "/dashboard",
        icon: <Vote />,
    },
    {
        title: "Xo'jaliklar",
        url: "/dashboard/village-people",
        icon: <Users />,
    },
]

export function AppSidebar() {
    return (
        <SidebarGroup>
            <SidebarGroupLabel>{"Ko'ktol ovozlar boshqaruv paneli"}</SidebarGroupLabel>
            <SidebarGroupContent>
                <SidebarMenu>
                    <>
                        {items.map((item) => (
                            <SidebarMenuItem key={item.title}>
                                <SidebarMenuButton asChild>
                                    <Link href={item.url}>
                                        {item.icon}
                                        <span>{item.title}</span>
                                    </Link>
                                </SidebarMenuButton>
                            </SidebarMenuItem>
                        ))}
                    </>
                </SidebarMenu>
            </SidebarGroupContent>
        </SidebarGroup>
    )
}