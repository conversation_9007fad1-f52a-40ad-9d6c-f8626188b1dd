import { Vote, Users, Plus, List } from "lucide-react"
import <PERSON> from "next/link";

import {
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
} from "@/shared/ui/sidebar"

// Menu items.
const items = [
    {
        title: "Ovozlar",
        url: "/dashboard",
        icon: <Vote />,
    },
    {
        title: "Xo'jaliklar",
        url: "/dashboard/village-people",
        icon: <Users />,
        subItems: [
            {
                title: "Ro'yxat",
                url: "/dashboard/village-people",
                icon: <List />,
            },
            {
                title: "Yangi qo'shish",
                url: "/dashboard/village-people/create",
                icon: <Plus />,
            },
        ]
    },
]

export function AppSidebar() {
    return (
        <SidebarGroup>
            <SidebarGroupLabel>{"Ko'ktol ovozlar boshqaruv paneli"}</SidebarGroupLabel>
            <SidebarGroupContent>
                <SidebarMenu>
                    <>
                        {items.map((item) => (
                            <SidebarMenuItem key={item.title}>
                                <SidebarMenuButton asChild>
                                    <Link href={item.url}>
                                        {item.icon}
                                        <span>{item.title}</span>
                                    </Link>
                                </SidebarMenuButton>
                                {item.subItems && (
                                    <SidebarMenuSub>
                                        {item.subItems.map((subItem) => (
                                            <SidebarMenuSubItem key={subItem.title}>
                                                <SidebarMenuSubButton asChild>
                                                    <Link href={subItem.url}>
                                                        {subItem.icon}
                                                        <span>{subItem.title}</span>
                                                    </Link>
                                                </SidebarMenuSubButton>
                                            </SidebarMenuSubItem>
                                        ))}
                                    </SidebarMenuSub>
                                )}
                            </SidebarMenuItem>
                        ))}
                    </>
                </SidebarMenu>
            </SidebarGroupContent>
        </SidebarGroup>
    )
}